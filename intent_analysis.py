import json
import os
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np
from collections import defaultdict, Counter
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class IntentAnalyzer:
    def __init__(self, input_dir="input", output_dir="output"):
        self.input_dir = input_dir
        self.output_dir = output_dir
        self.matched_data = {}  # 存储匹配的输入输出数据
        
    def load_and_match_data(self):
        """加载并匹配输入输出数据"""
        print("正在加载并匹配输入输出数据...")
        
        # 加载输入数据
        input_data = {}
        for topic_dir in os.listdir(self.input_dir):
            if not os.path.isdir(os.path.join(self.input_dir, topic_dir)):
                continue
                
            topic_name = topic_dir.replace("_intera_data", "")
            input_data[topic_name] = {}
            
            intera_data_path = os.path.join(self.input_dir, topic_dir, "intera_data")
            if not os.path.exists(intera_data_path):
                continue
                
            for file_name in os.listdir(intera_data_path):
                if file_name.endswith('.json'):
                    user_id = file_name.replace('.json', '')
                    file_path = os.path.join(intera_data_path, file_name)
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            data = json.load(f)
                            input_data[topic_name][user_id] = data
                    except Exception as e:
                        print(f"读取输入文件 {file_path} 时出错: {e}")
        
        # 加载输出数据并匹配
        for topic_dir in os.listdir(self.output_dir):
            if not os.path.isdir(os.path.join(self.output_dir, topic_dir)):
                continue
                
            topic_name = topic_dir.replace("_output", "")
            if topic_name not in input_data:
                continue
                
            self.matched_data[topic_name] = []
            topic_path = os.path.join(self.output_dir, topic_dir)
            
            for user_dir in os.listdir(topic_path):
                user_path = os.path.join(topic_path, user_dir)
                if not os.path.isdir(user_path):
                    continue
                    
                user_id = user_dir
                if user_id not in input_data[topic_name]:
                    continue
                    
                for file_name in os.listdir(user_path):
                    if file_name.endswith('.json'):
                        file_path = os.path.join(user_path, file_name)
                        try:
                            with open(file_path, 'r', encoding='utf-8') as f:
                                output_data = json.load(f)
                                
                                # 匹配输入输出数据
                                input_user_data = input_data[topic_name][user_id]
                                interaction_index = output_data.get('interaction_index', 0)
                                
                                if interaction_index < len(input_user_data.get('interactions', [])):
                                    matched_item = {
                                        'topic': topic_name,
                                        'user_id': user_id,
                                        'interaction_index': interaction_index,
                                        'input_data': input_user_data['interactions'][interaction_index],
                                        'output_data': output_data,
                                        'intent_label': output_data.get('final_structured_intent_label', {})
                                    }
                                    self.matched_data[topic_name].append(matched_item)
                        except Exception as e:
                            print(f"读取输出文件 {file_path} 时出错: {e}")
        
        total_matched = sum(len(data) for data in self.matched_data.values())
        print(f"数据匹配完成，共匹配 {total_matched} 条记录")
        
    def analyze_input_output_relationships(self):
        """分析输入输出关系"""
        print("正在分析输入输出关系...")
        
        analysis_results = {}
        
        for topic, data_list in self.matched_data.items():
            print(f"\n分析主题: {topic}")
            
            topic_analysis = {
                'interaction_type_vs_intent': defaultdict(Counter),
                'metrics_vs_intent': defaultdict(list),
                'text_length_vs_intent': defaultdict(list),
                'intent_confidence_analysis': {},
                'behavior_pattern_analysis': {},
                'emotional_mapping': defaultdict(Counter)
            }
            
            for item in data_list:
                input_data = item['input_data']
                intent_label = item['intent_label']
                
                # 提取关键字段
                interaction_type = input_data.get('type', 'unknown')
                intent_category = intent_label.get('coarse_intent_category', 'unknown')
                confidence_score = intent_label.get('confidence_score', 0)
                text = input_data.get('text', '')
                metrics = input_data.get('metrics', {})
                
                # 1. 交互类型 vs 意图类别
                topic_analysis['interaction_type_vs_intent'][interaction_type][intent_category] += 1
                
                # 2. 社交媒体指标 vs 意图类别
                for metric_name, metric_value in metrics.items():
                    if isinstance(metric_value, (int, float)):
                        topic_analysis['metrics_vs_intent'][f"{intent_category}_{metric_name}"].append(metric_value)
                
                # 3. 文本长度 vs 意图类别
                topic_analysis['text_length_vs_intent'][intent_category].append(len(text))
                
                # 4. 置信度分析
                if intent_category not in topic_analysis['intent_confidence_analysis']:
                    topic_analysis['intent_confidence_analysis'][intent_category] = []
                topic_analysis['intent_confidence_analysis'][intent_category].append(confidence_score)
                
                # 5. 行为模式分析
                behavior_set = intent_label.get('behavior_set', [])
                for behavior in behavior_set:
                    behavior_type = behavior.get('behavior_type', 'unknown')
                    if behavior_type not in topic_analysis['behavior_pattern_analysis']:
                        topic_analysis['behavior_pattern_analysis'][behavior_type] = defaultdict(Counter)
                    topic_analysis['behavior_pattern_analysis'][behavior_type]['intent_category'][intent_category] += 1
                    topic_analysis['behavior_pattern_analysis'][behavior_type]['interaction_type'][interaction_type] += 1
                
                # 6. 情感映射
                output_analysis = item['output_data'].get('context_analysis', {}).get('analysis', {})
                emotional_tendency = output_analysis.get('emotional_tendency', {})
                overall_sentiment = emotional_tendency.get('overall_sentiment', 'unknown')
                topic_analysis['emotional_mapping'][intent_category][overall_sentiment] += 1
            
            analysis_results[topic] = topic_analysis
        
        return analysis_results
    
    def create_relationship_visualizations(self, analysis_results):
        """创建关系可视化图表"""
        print("正在创建关系可视化图表...")
        
        os.makedirs('intent_charts', exist_ok=True)
        
        # 1. 交互类型 vs 意图类别热力图
        self.plot_interaction_intent_heatmap(analysis_results)
        
        # 2. 社交媒体指标 vs 意图类别
        self.plot_metrics_vs_intent(analysis_results)
        
        # 3. 文本长度 vs 意图类别
        self.plot_text_length_vs_intent(analysis_results)
        
        # 4. 置信度分析
        self.plot_confidence_analysis(analysis_results)
        
        # 5. 行为模式分析
        self.plot_behavior_patterns(analysis_results)
        
        # 6. 情感映射
        self.plot_emotional_mapping(analysis_results)
        
        print("关系图表创建完成，保存在 intent_charts/ 目录下")
    
    def plot_interaction_intent_heatmap(self, analysis_results):
        """绘制交互类型 vs 意图类别热力图"""
        fig, axes = plt.subplots(1, len(analysis_results), figsize=(6*len(analysis_results), 8))
        if len(analysis_results) == 1:
            axes = [axes]
        
        fig.suptitle('交互类型 vs 意图类别关系热力图', fontsize=16, fontweight='bold')
        
        for i, (topic, analysis) in enumerate(analysis_results.items()):
            interaction_intent_data = analysis['interaction_type_vs_intent']
            
            # 构建数据矩阵
            all_interactions = set()
            all_intents = set()
            for interaction_type, intent_counter in interaction_intent_data.items():
                all_interactions.add(interaction_type)
                all_intents.update(intent_counter.keys())
            
            all_interactions = sorted(list(all_interactions))
            all_intents = sorted(list(all_intents))
            
            matrix = np.zeros((len(all_interactions), len(all_intents)))
            for j, interaction in enumerate(all_interactions):
                for k, intent in enumerate(all_intents):
                    matrix[j, k] = interaction_intent_data[interaction][intent]
            
            # 绘制热力图
            im = axes[i].imshow(matrix, cmap='YlOrRd', aspect='auto')
            axes[i].set_xticks(range(len(all_intents)))
            axes[i].set_xticklabels(all_intents, rotation=45, ha='right')
            axes[i].set_yticks(range(len(all_interactions)))
            axes[i].set_yticklabels(all_interactions)
            axes[i].set_title(f'{topic}')
            
            # 添加数值标注
            for j in range(len(all_interactions)):
                for k in range(len(all_intents)):
                    if matrix[j, k] > 0:
                        axes[i].text(k, j, f'{int(matrix[j, k])}', 
                                   ha='center', va='center', color='white' if matrix[j, k] > matrix.max()/2 else 'black')
            
            plt.colorbar(im, ax=axes[i])
        
        plt.tight_layout()
        plt.savefig('intent_charts/interaction_intent_heatmap.png', dpi=300, bbox_inches='tight')
        plt.close()
    
    def plot_metrics_vs_intent(self, analysis_results):
        """绘制社交媒体指标 vs 意图类别"""
        metrics = ['like_count', 'retweet_count', 'reply_count', 'view_count']
        
        for metric in metrics:
            fig, axes = plt.subplots(1, len(analysis_results), figsize=(6*len(analysis_results), 6))
            if len(analysis_results) == 1:
                axes = [axes]
            
            fig.suptitle(f'{metric.replace("_", " ").title()} vs 意图类别', fontsize=16, fontweight='bold')
            
            for i, (topic, analysis) in enumerate(analysis_results.items()):
                intent_metrics = {}
                for key, values in analysis['metrics_vs_intent'].items():
                    if key.endswith(f'_{metric}') and values:
                        intent_category = key.replace(f'_{metric}', '')
                        intent_metrics[intent_category] = values
                
                if intent_metrics:
                    # 创建箱线图
                    data_to_plot = []
                    labels = []
                    for intent, values in intent_metrics.items():
                        if values:  # 确保有数据
                            data_to_plot.append(values)
                            labels.append(intent)
                    
                    if data_to_plot:
                        bp = axes[i].boxplot(data_to_plot, labels=labels, patch_artist=True)
                        for patch in bp['boxes']:
                            patch.set_facecolor('lightblue')
                        axes[i].set_title(f'{topic}')
                        axes[i].set_ylabel(metric.replace('_', ' ').title())
                        axes[i].tick_params(axis='x', rotation=45)
                        axes[i].set_yscale('log')  # 使用对数刻度
                    else:
                        axes[i].text(0.5, 0.5, '无数据', ha='center', va='center', transform=axes[i].transAxes)
                else:
                    axes[i].text(0.5, 0.5, '无数据', ha='center', va='center', transform=axes[i].transAxes)
            
            plt.tight_layout()
            plt.savefig(f'intent_charts/{metric}_vs_intent.png', dpi=300, bbox_inches='tight')
            plt.close()
    
    def plot_text_length_vs_intent(self, analysis_results):
        """绘制文本长度 vs 意图类别"""
        fig, axes = plt.subplots(1, len(analysis_results), figsize=(6*len(analysis_results), 6))
        if len(analysis_results) == 1:
            axes = [axes]
        
        fig.suptitle('文本长度 vs 意图类别', fontsize=16, fontweight='bold')
        
        for i, (topic, analysis) in enumerate(analysis_results.items()):
            text_length_data = analysis['text_length_vs_intent']
            
            if text_length_data:
                data_to_plot = []
                labels = []
                for intent, lengths in text_length_data.items():
                    if lengths:
                        data_to_plot.append(lengths)
                        labels.append(intent)
                
                if data_to_plot:
                    bp = axes[i].boxplot(data_to_plot, labels=labels, patch_artist=True)
                    for patch in bp['boxes']:
                        patch.set_facecolor('lightgreen')
                    axes[i].set_title(f'{topic}')
                    axes[i].set_ylabel('文本长度(字符数)')
                    axes[i].tick_params(axis='x', rotation=45)
                else:
                    axes[i].text(0.5, 0.5, '无数据', ha='center', va='center', transform=axes[i].transAxes)
            else:
                axes[i].text(0.5, 0.5, '无数据', ha='center', va='center', transform=axes[i].transAxes)
        
        plt.tight_layout()
        plt.savefig('intent_charts/text_length_vs_intent.png', dpi=300, bbox_inches='tight')
        plt.close()

    def plot_confidence_analysis(self, analysis_results):
        """绘制置信度分析"""
        fig, axes = plt.subplots(1, len(analysis_results), figsize=(6*len(analysis_results), 6))
        if len(analysis_results) == 1:
            axes = [axes]

        fig.suptitle('意图类别置信度分析', fontsize=16, fontweight='bold')

        for i, (topic, analysis) in enumerate(analysis_results.items()):
            confidence_data = analysis['intent_confidence_analysis']

            if confidence_data:
                data_to_plot = []
                labels = []
                for intent, confidences in confidence_data.items():
                    if confidences:
                        data_to_plot.append(confidences)
                        labels.append(intent)

                if data_to_plot:
                    bp = axes[i].boxplot(data_to_plot, labels=labels, patch_artist=True)
                    for patch in bp['boxes']:
                        patch.set_facecolor('gold')
                    axes[i].set_title(f'{topic}')
                    axes[i].set_ylabel('置信度')
                    axes[i].set_ylim(0, 1)
                    axes[i].tick_params(axis='x', rotation=45)
                else:
                    axes[i].text(0.5, 0.5, '无数据', ha='center', va='center', transform=axes[i].transAxes)
            else:
                axes[i].text(0.5, 0.5, '无数据', ha='center', va='center', transform=axes[i].transAxes)

        plt.tight_layout()
        plt.savefig('intent_charts/confidence_analysis.png', dpi=300, bbox_inches='tight')
        plt.close()

    def plot_behavior_patterns(self, analysis_results):
        """绘制行为模式分析"""
        for topic, analysis in analysis_results.items():
            behavior_data = analysis['behavior_pattern_analysis']

            if not behavior_data:
                continue

            fig, axes = plt.subplots(len(behavior_data), 2, figsize=(12, 4*len(behavior_data)))
            if len(behavior_data) == 1:
                axes = axes.reshape(1, -1)

            fig.suptitle(f'{topic} - 行为模式分析', fontsize=16, fontweight='bold')

            for i, (behavior_type, patterns) in enumerate(behavior_data.items()):
                # 行为类型 vs 意图类别
                intent_counts = patterns['intent_category']
                if intent_counts:
                    intents = list(intent_counts.keys())
                    counts = list(intent_counts.values())
                    axes[i, 0].bar(intents, counts, color='lightcoral', alpha=0.7)
                    axes[i, 0].set_title(f'{behavior_type} - 意图类别分布')
                    axes[i, 0].set_ylabel('数量')
                    axes[i, 0].tick_params(axis='x', rotation=45)

                # 行为类型 vs 交互类型
                interaction_counts = patterns['interaction_type']
                if interaction_counts:
                    interactions = list(interaction_counts.keys())
                    counts = list(interaction_counts.values())
                    axes[i, 1].bar(interactions, counts, color='lightblue', alpha=0.7)
                    axes[i, 1].set_title(f'{behavior_type} - 交互类型分布')
                    axes[i, 1].set_ylabel('数量')
                    axes[i, 1].tick_params(axis='x', rotation=45)

            plt.tight_layout()
            plt.savefig(f'intent_charts/{topic}_behavior_patterns.png', dpi=300, bbox_inches='tight')
            plt.close()

    def plot_emotional_mapping(self, analysis_results):
        """绘制情感映射"""
        fig, axes = plt.subplots(1, len(analysis_results), figsize=(8*len(analysis_results), 6))
        if len(analysis_results) == 1:
            axes = [axes]

        fig.suptitle('意图类别 vs 情感倾向映射', fontsize=16, fontweight='bold')

        for i, (topic, analysis) in enumerate(analysis_results.items()):
            emotional_data = analysis['emotional_mapping']

            if emotional_data:
                # 构建数据矩阵
                all_intents = list(emotional_data.keys())
                all_emotions = set()
                for emotion_counter in emotional_data.values():
                    all_emotions.update(emotion_counter.keys())
                all_emotions = sorted(list(all_emotions))

                matrix = np.zeros((len(all_intents), len(all_emotions)))
                for j, intent in enumerate(all_intents):
                    for k, emotion in enumerate(all_emotions):
                        matrix[j, k] = emotional_data[intent][emotion]

                # 绘制热力图
                im = axes[i].imshow(matrix, cmap='RdYlBu_r', aspect='auto')
                axes[i].set_xticks(range(len(all_emotions)))
                axes[i].set_xticklabels(all_emotions, rotation=45, ha='right')
                axes[i].set_yticks(range(len(all_intents)))
                axes[i].set_yticklabels(all_intents)
                axes[i].set_title(f'{topic}')

                # 添加数值标注
                for j in range(len(all_intents)):
                    for k in range(len(all_emotions)):
                        if matrix[j, k] > 0:
                            axes[i].text(k, j, f'{int(matrix[j, k])}',
                                       ha='center', va='center',
                                       color='white' if matrix[j, k] > matrix.max()/2 else 'black')

                plt.colorbar(im, ax=axes[i])
            else:
                axes[i].text(0.5, 0.5, '无数据', ha='center', va='center', transform=axes[i].transAxes)

        plt.tight_layout()
        plt.savefig('intent_charts/emotional_mapping.png', dpi=300, bbox_inches='tight')
        plt.close()

    def generate_relationship_report(self, analysis_results):
        """生成关系分析报告"""
        print("\n" + "="*60)
        print("输入输出数据关系分析报告")
        print("="*60)

        for topic, analysis in analysis_results.items():
            print(f"\n【{topic}话题分析】")

            # 1. 交互类型 vs 意图类别分析
            print(f"\n1. 交互类型 vs 意图类别关系:")
            interaction_intent_data = analysis['interaction_type_vs_intent']
            for interaction_type, intent_counter in interaction_intent_data.items():
                print(f"  {interaction_type}:")
                for intent, count in intent_counter.most_common(3):
                    percentage = count / sum(intent_counter.values()) * 100
                    print(f"    - {intent}: {count}次 ({percentage:.1f}%)")

            # 2. 置信度分析
            print(f"\n2. 各意图类别置信度分析:")
            confidence_data = analysis['intent_confidence_analysis']
            for intent, confidences in confidence_data.items():
                if confidences:
                    avg_confidence = np.mean(confidences)
                    std_confidence = np.std(confidences)
                    print(f"  {intent}: 平均{avg_confidence:.3f} (±{std_confidence:.3f})")

            # 3. 文本长度分析
            print(f"\n3. 各意图类别文本长度特征:")
            text_length_data = analysis['text_length_vs_intent']
            for intent, lengths in text_length_data.items():
                if lengths:
                    avg_length = np.mean(lengths)
                    print(f"  {intent}: 平均{avg_length:.1f}字符")

            # 4. 行为模式分析
            print(f"\n4. 行为模式分析:")
            behavior_data = analysis['behavior_pattern_analysis']
            for behavior_type, patterns in behavior_data.items():
                intent_counts = patterns['intent_category']
                top_intent = intent_counts.most_common(1)[0] if intent_counts else ('无', 0)
                print(f"  {behavior_type}: 主要意图类别 - {top_intent[0]} ({top_intent[1]}次)")

            # 5. 情感映射分析
            print(f"\n5. 意图-情感映射:")
            emotional_data = analysis['emotional_mapping']
            for intent, emotion_counter in emotional_data.items():
                if emotion_counter:
                    top_emotion = emotion_counter.most_common(1)[0]
                    total = sum(emotion_counter.values())
                    percentage = top_emotion[1] / total * 100
                    print(f"  {intent}: 主要情感 - {top_emotion[0]} ({percentage:.1f}%)")

        print("\n" + "="*60)

    def run_intent_analysis(self):
        """运行完整的意图分析"""
        # 加载并匹配数据
        self.load_and_match_data()

        # 分析输入输出关系
        analysis_results = self.analyze_input_output_relationships()

        # 创建可视化
        self.create_relationship_visualizations(analysis_results)

        # 生成报告
        self.generate_relationship_report(analysis_results)

        return analysis_results

if __name__ == "__main__":
    analyzer = IntentAnalyzer()
    results = analyzer.run_intent_analysis()
