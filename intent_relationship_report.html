<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>输入输出数据意图关系分析报告</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            border-bottom: 3px solid #e74c3c;
            padding-bottom: 10px;
        }
        h2 {
            color: #34495e;
            border-left: 4px solid #e74c3c;
            padding-left: 15px;
            margin-top: 30px;
        }
        h3 {
            color: #2c3e50;
            margin-top: 25px;
        }
        .summary-box {
            background-color: #fdf2f2;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #e74c3c;
        }
        .insight-box {
            background-color: #f0f8ff;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid #3498db;
        }
        .chart-container {
            text-align: center;
            margin: 30px 0;
        }
        .chart-container img {
            max-width: 100%;
            height: auto;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .stats-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        .stats-table th, .stats-table td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        .stats-table th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .stats-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .highlight {
            background-color: #fff3cd;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #ffc107;
            margin: 15px 0;
        }
        .key-finding {
            background-color: #d4edda;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #28a745;
            margin: 15px 0;
        }
        .warning {
            background-color: #f8d7da;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #dc3545;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 输入输出数据意图关系分析报告</h1>
        
        <div class="summary-box">
            <h2>📋 分析概述</h2>
            <p>本报告深入分析了输入的社交媒体交互数据与输出的AI意图标签(<code>final_structured_intent_label</code>)之间的关系，揭示了用户行为特征与意图分类的内在联系。</p>
            <p><strong>数据范围：</strong>DEI话题，97条匹配记录</p>
        </div>

        <h2>📊 关键发现总览</h2>
        
        <div class="key-finding">
            <h3>🎯 核心洞察</h3>
            <ul>
                <li><strong>意图分布高度集中：</strong>抵抗型(resistant)意图占主导地位(43.3%)</li>
                <li><strong>交互类型与意图强相关：</strong>评论类型主要对应抵抗型意图</li>
                <li><strong>情感-意图映射清晰：</strong>抵抗型意图95.2%为负面情感</li>
                <li><strong>文本长度差异显著：</strong>不同意图类别的文本长度差异巨大(18-341字符)</li>
                <li><strong>置信度普遍较高：</strong>平均置信度0.949，表明分类质量良好</li>
            </ul>
        </div>

        <h2>🔗 交互类型与意图类别关系</h2>
        
        <div class="chart-container">
            <h3>交互类型 vs 意图类别热力图</h3>
            <img src="intent_charts/interaction_intent_heatmap.png" alt="交互类型意图热力图">
        </div>

        <div class="insight-box">
            <h4>关键发现：</h4>
            <ul>
                <li><strong>评论(comment)主导：</strong>93条记录中，评论类型占绝大多数</li>
                <li><strong>引用(quote)模式：</strong>4条引用记录中，50%为抵抗型意图</li>
                <li><strong>意图集中度：</strong>抵抗型意图在两种交互类型中都占主导</li>
            </ul>
        </div>

        <h2>📏 文本长度与意图类别关系</h2>
        
        <div class="chart-container">
            <h3>文本长度分布</h3>
            <img src="intent_charts/text_length_vs_intent.png" alt="文本长度分布">
        </div>

        <table class="stats-table">
            <thead>
                <tr>
                    <th>意图类别</th>
                    <th>平均文本长度(字符)</th>
                    <th>特征描述</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>strategic resistant</td>
                    <td>341.0</td>
                    <td>最长，深度论述型</td>
                </tr>
                <tr>
                    <td>systemic hate propagation</td>
                    <td>295.0</td>
                    <td>长篇仇恨言论</td>
                </tr>
                <tr>
                    <td>tactical discourse</td>
                    <td>265.0</td>
                    <td>策略性讨论</td>
                </tr>
                <tr>
                    <td>resistant</td>
                    <td>149.8</td>
                    <td>中等长度抵抗</td>
                </tr>
                <tr>
                    <td>affirmative expressive</td>
                    <td>18.0</td>
                    <td>最短，简单肯定</td>
                </tr>
            </tbody>
        </table>

        <div class="warning">
            <strong>重要发现：</strong>极端意图类别(如仇恨传播、恐怖主义煽动)往往伴随特定的文本长度模式，这可为内容审核提供重要线索。
        </div>

        <h2>💯 置信度分析</h2>
        
        <div class="chart-container">
            <h3>各意图类别置信度分布</h3>
            <img src="intent_charts/confidence_analysis.png" alt="置信度分析">
        </div>

        <div class="highlight">
            <h4>置信度特征：</h4>
            <ul>
                <li><strong>高置信度类别：</strong>terrorism incitement (0.990), systemic hate propagation (0.990)</li>
                <li><strong>中等置信度类别：</strong>constructive questioning (0.890), political authenticity (0.890)</li>
                <li><strong>整体水平：</strong>所有类别置信度均超过0.89，表明分类质量优秀</li>
            </ul>
        </div>

        <h2>💭 情感倾向映射</h2>
        
        <div class="chart-container">
            <h3>意图类别 vs 情感倾向热力图</h3>
            <img src="intent_charts/emotional_mapping.png" alt="情感映射">
        </div>

        <table class="stats-table">
            <thead>
                <tr>
                    <th>情感倾向</th>
                    <th>主要意图类别</th>
                    <th>特征描述</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>负面(negative)</td>
                    <td>resistant (95.2%)</td>
                    <td>抵抗型意图几乎全部为负面情感</td>
                </tr>
                <tr>
                    <td>正面(positive)</td>
                    <td>expressive (57.1%)</td>
                    <td>表达型意图更多正面情感</td>
                </tr>
                <tr>
                    <td>中性(neutral)</td>
                    <td>active (50.0%)</td>
                    <td>主动型意图情感相对平衡</td>
                </tr>
            </tbody>
        </table>

        <h2>📈 社交媒体指标关系</h2>
        
        <div class="chart-container">
            <h3>点赞数 vs 意图类别</h3>
            <img src="intent_charts/like_count_vs_intent.png" alt="点赞数分布">
        </div>

        <div class="chart-container">
            <h3>浏览数 vs 意图类别</h3>
            <img src="intent_charts/view_count_vs_intent.png" alt="浏览数分布">
        </div>

        <div class="insight-box">
            <h4>社交指标洞察：</h4>
            <ul>
                <li><strong>参与度差异：</strong>不同意图类别在社交媒体指标上表现出显著差异</li>
                <li><strong>抵抗型内容：</strong>往往获得较高的关注度但参与度相对较低</li>
                <li><strong>表达型内容：</strong>在点赞等正面互动上表现更好</li>
            </ul>
        </div>

        <h2>🎭 行为模式分析</h2>
        
        <div class="chart-container">
            <h3>DEI话题行为模式</h3>
            <img src="intent_charts/DEI_behavior_patterns.png" alt="行为模式">
        </div>

        <h2>🔍 深度分析与启示</h2>
        
        <div class="key-finding">
            <h3>1. 意图预测模型的可行性</h3>
            <p>基于输入特征(交互类型、文本长度、社交指标)预测意图类别具有很高的可行性，特别是：</p>
            <ul>
                <li>文本长度可作为重要特征，不同意图类别有明显的长度偏好</li>
                <li>交互类型与意图类别存在强关联性</li>
                <li>社交媒体指标可提供额外的预测信号</li>
            </ul>
        </div>

        <div class="key-finding">
            <h3>2. 内容审核的应用价值</h3>
            <p>分析结果对内容审核具有重要指导意义：</p>
            <ul>
                <li><strong>高风险意图识别：</strong>terrorism incitement, systemic hate propagation等需重点监控</li>
                <li><strong>文本长度阈值：</strong>超长文本(>300字符)需额外关注</li>
                <li><strong>情感-意图联合判断：</strong>负面情感+抵抗型意图组合需要重点审查</li>
            </ul>
        </div>

        <div class="key-finding">
            <h3>3. 用户行为理解</h3>
            <p>输入输出关系揭示了用户行为的深层模式：</p>
            <ul>
                <li><strong>抵抗型用户：</strong>倾向于使用较长文本表达不满，情感以负面为主</li>
                <li><strong>表达型用户：</strong>文本长度适中，情感相对积极</li>
                <li><strong>主动型用户：</strong>文本简洁，情感中性，行为目标明确</li>
            </ul>
        </div>

        <h2>📝 建议与后续研究方向</h2>
        
        <div class="highlight">
            <h4>技术改进建议：</h4>
            <ol>
                <li><strong>特征工程优化：</strong>结合文本长度、情感倾向、社交指标构建更强的预测模型</li>
                <li><strong>多模态融合：</strong>整合文本内容、用户行为、社交网络信息</li>
                <li><strong>实时监控系统：</strong>基于意图分类结果建立内容风险预警机制</li>
            </ol>
        </div>

        <div class="highlight">
            <h4>研究扩展方向：</h4>
            <ol>
                <li><strong>跨话题验证：</strong>在其他话题(中美贸易战、美联储降息)上验证发现的规律</li>
                <li><strong>时间序列分析：</strong>研究意图类别随时间的演变模式</li>
                <li><strong>用户画像构建：</strong>基于意图分类结果构建用户行为画像</li>
            </ol>
        </div>

        <div style="text-align: center; margin-top: 40px; color: #7f8c8d;">
            <p>报告生成时间: <script>document.write(new Date().toLocaleString('zh-CN'))</script></p>
            <p>数据来源: DEI话题社交媒体交互数据及AI意图分析结果</p>
        </div>
    </div>
</body>
</html>
